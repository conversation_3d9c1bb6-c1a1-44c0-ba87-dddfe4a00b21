import pandas as pd
import numpy as np
import os
import time
from datetime import datetime, timedelta
import random

from utils import (
    FileProcessor, ColumnMapper, DataStandardizer, ErrorHandler, PerformanceMonitor,
    setup_logging, read_file_data, save_processed_data
)

def create_sample_data():
    """Create sample broker and trader data files for testing"""
    
    # Create test_data directory
    if not os.path.exists('test_data'):
        os.makedirs('test_data')
    
    # Sample broker data with various column name variations
    broker_data = {
        'trade_date': pd.date_range('2024-01-01', periods=100, freq='D'),
        'symbol': ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'AMZN'] * 20,
        'market': ['NSE', 'BSE', 'NYSE', 'NASDAQ'] * 25,
        'exp_date': pd.date_range('2024-03-15', periods=100, freq='D'),
        'strike_price': np.random.uniform(100, 500, 100).round(2),
        'call_put': np.random.choice(['CALL', 'PUT'], 100),
        'buy_quantity': np.random.randint(1, 1000, 100),
        'buy_amount': np.random.uniform(1000, 50000, 100).round(2),
        'sell_quantity': np.random.randint(0, 500, 100),
        'sell_amount': np.random.uniform(0, 25000, 100).round(2)
    }
    
    broker_df = pd.DataFrame(broker_data)
    broker_df.to_csv('test_data/sample_broker_data.csv', index=False)
    broker_df.to_excel('test_data/sample_broker_data.xlsx', index=False)
    
    # Sample trader data with different column names
    trader_data = {
        'Date': pd.date_range('2024-01-01', periods=80, freq='D'),
        'Underlying': ['AAPL', 'GOOGL', 'MSFT', 'TSLA'] * 20,
        'Exchange': ['NSE', 'BSE'] * 40,
        'Expiry': pd.date_range('2024-03-15', periods=80, freq='D'),
        'Strike': np.random.uniform(100, 500, 80).round(2),
        'Option Type': np.random.choice(['C', 'P'], 80),
        'Buy Qty': np.random.randint(1, 500, 80),
        'Buy Value': np.random.uniform(500, 25000, 80).round(2),
        'Sell Qty': np.random.randint(0, 300, 80),
        'Sell Value': np.random.uniform(0, 15000, 80).round(2)
    }
    
    trader_df = pd.DataFrame(trader_data)
    trader_df.to_csv('test_data/sample_trader_data.csv', index=False)
    trader_df.to_excel('test_data/sample_trader_data.xlsx', index=False)
    
    # Create problematic data for edge case testing
    problematic_data = {
        'dt': ['2024-01-01', '2024-01-02', 'invalid_date', '2024-01-04'],
        'stock': ['AAPL', '', 'GOOGL', 'MSFT'],
        'exch': ['NSE', 'BSE', 'NYSE', ''],
        'maturity': ['2024-03-15', '2024-03-16', '2024-03-17', 'invalid'],
        'k': [100.5, 'invalid', 200.0, 150.75],
        'type': ['CALL', 'PUT', 'C', 'P'],
        'bought': [100, 0, 'invalid', 200],
        'buy_val': [5000.0, 0, 10000, 'invalid'],
        'sold': [50, 0, 100, 150],
        'sell_val': [2500, 0, 5000, 7500]
    }
    
    problematic_df = pd.DataFrame(problematic_data)
    problematic_df.to_csv('test_data/problematic_data.csv', index=False)
    
    print("✅ Sample test data created successfully!")
    return True

def test_file_processing():
    """Test file processing functionality"""
    print("\n🧪 Testing File Processing...")
    
    processor = FileProcessor()
    
    # Test supported file detection
    assert processor.is_supported_file('test.xlsx') == True
    assert processor.is_supported_file('test.csv') == True
    assert processor.is_supported_file('test.txt') == False
    
    # Test duplicate detection
    test_content = b"test content"
    assert processor.is_duplicate_file(test_content) == False
    assert processor.is_duplicate_file(test_content) == True  # Should be duplicate now
    
    print("✅ File processing tests passed!")

def test_column_mapping():
    """Test column mapping functionality"""
    print("\n🧪 Testing Column Mapping...")
    
    mapper = ColumnMapper()
    
    # Test fuzzy matching
    df_columns = ['trade_date', 'symbol', 'strike_price', 'call_put', 'buy_quantity']
    auto_mappings, unmapped = mapper.get_auto_mappings(df_columns, 'broker')
    
    # Should map most columns automatically
    assert len(auto_mappings) >= 3, f"Expected at least 3 auto-mappings, got {len(auto_mappings)}"
    
    # Test mapping validation
    test_mappings = {
        'trade_date': 'Date',
        'symbol': 'Underlying',
        'strike_price': 'Strike'
    }
    
    is_valid, errors = mapper.validate_mappings(test_mappings)
    assert is_valid == True, f"Mapping validation failed: {errors}"
    
    print("✅ Column mapping tests passed!")

def test_data_standardization():
    """Test data standardization functionality"""
    print("\n🧪 Testing Data Standardization...")
    
    standardizer = DataStandardizer()
    
    # Create test dataframe
    test_data = {
        'trade_date': ['2024-01-01', '2024-01-02'],
        'symbol': ['AAPL', 'GOOGL'],
        'strike_price': [100.5, 200.0],
        'call_put': ['CALL', 'PUT'],
        'buy_quantity': [100, 200]
    }
    
    test_df = pd.DataFrame(test_data)
    
    # Test column mappings
    mappings = {
        'trade_date': 'Date',
        'symbol': 'Underlying',
        'strike_price': 'Strike',
        'call_put': 'Option Type',
        'buy_quantity': 'Buy Qty'
    }
    
    standardized_df = standardizer.standardize_dataframe(test_df, mappings)
    
    # Check if all standard columns are present
    expected_columns = standardizer.standard_columns
    for col in expected_columns:
        assert col in standardized_df.columns, f"Missing standard column: {col}"
    
    # Check data formatting
    assert standardized_df['Option Type'].iloc[0] == 'CALL'
    assert standardized_df['Option Type'].iloc[1] == 'PUT'
    
    print("✅ Data standardization tests passed!")

def test_error_handling():
    """Test error handling functionality"""
    print("\n🧪 Testing Error Handling...")
    
    error_handler = ErrorHandler()
    
    # Test user-friendly error messages
    test_errors = [
        FileNotFoundError("No such file or directory"),
        PermissionError("Permission denied"),
        ValueError("Unsupported format"),
        MemoryError("Out of memory")
    ]
    
    for error in test_errors:
        friendly_msg = error_handler.get_user_friendly_error(error)
        assert friendly_msg.startswith("❌"), f"Error message should start with ❌: {friendly_msg}"
        assert len(friendly_msg) > 10, "Error message too short"
    
    print("✅ Error handling tests passed!")

def test_performance():
    """Test system performance with multiple files"""
    print("\n🧪 Testing Performance...")
    
    monitor = PerformanceMonitor()
    
    # Test timer functionality
    monitor.start_timer("test_operation")
    time.sleep(0.1)  # Simulate work
    duration = monitor.end_timer("test_operation")
    
    assert duration >= 0.1, f"Timer should measure at least 0.1 seconds, got {duration}"
    
    # Test file size limits
    small_file_size = 1024 * 1024  # 1MB
    large_file_size = 100 * 1024 * 1024  # 100MB
    
    assert monitor.check_file_size_limits(small_file_size, "small.csv") == True
    assert monitor.check_file_size_limits(large_file_size, "large.csv") == False
    
    print("✅ Performance tests passed!")

def test_end_to_end():
    """Test complete end-to-end workflow"""
    print("\n🧪 Testing End-to-End Workflow...")
    
    try:
        # Initialize components
        processor = FileProcessor()
        mapper = ColumnMapper()
        standardizer = DataStandardizer()
        
        # Read test files
        broker_df = read_file_data('test_data/sample_broker_data.csv')
        trader_df = read_file_data('test_data/sample_trader_data.csv')
        
        assert not broker_df.empty, "Broker data should not be empty"
        assert not trader_df.empty, "Trader data should not be empty"
        
        # Test column mapping
        broker_mappings, _ = mapper.get_auto_mappings(list(broker_df.columns), 'broker')
        trader_mappings, _ = mapper.get_auto_mappings(list(trader_df.columns), 'trader')
        
        # Standardize data
        broker_standardized = standardizer.standardize_dataframe(broker_df, broker_mappings)
        trader_standardized = standardizer.standardize_dataframe(trader_df, trader_mappings)
        
        # Validate results
        assert not broker_standardized.empty, "Standardized broker data should not be empty"
        assert not trader_standardized.empty, "Standardized trader data should not be empty"
        
        # Test data combination
        combined_broker = standardizer.combine_datasets([broker_standardized])
        combined_trader = standardizer.combine_datasets([trader_standardized])
        
        # Save results
        broker_path, trader_path = save_processed_data(combined_broker, combined_trader)
        
        assert os.path.exists(broker_path), "Broker output file should exist"
        assert os.path.exists(trader_path), "Trader output file should exist"
        
        print("✅ End-to-end workflow test passed!")
        
    except Exception as e:
        print(f"❌ End-to-end test failed: {str(e)}")
        raise

def run_all_tests():
    """Run all test functions"""
    print("🚀 Starting Financial Data Reconciliation System Tests")
    print("=" * 60)
    
    start_time = time.time()
    
    try:
        # Create sample data
        create_sample_data()
        
        # Run individual tests
        test_file_processing()
        test_column_mapping()
        test_data_standardization()
        test_error_handling()
        test_performance()
        
        # Run end-to-end test
        test_end_to_end()
        
        total_time = time.time() - start_time
        
        print("\n" + "=" * 60)
        print(f"🎉 All tests passed successfully in {total_time:.2f} seconds!")
        print("✅ System is ready for production use.")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Tests failed: {str(e)}")
        return False

if __name__ == "__main__":
    # Setup logging
    logger = setup_logging()
    
    # Run tests
    success = run_all_tests()
    
    if not success:
        exit(1)
