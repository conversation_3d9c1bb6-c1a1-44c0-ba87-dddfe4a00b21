import hashlib
import json
import logging
import os
import pandas as pd
import numpy as np
import time
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any
from fuzzywuzzy import fuzz, process
import chardet


def setup_logging():
    """Setup logging configuration"""
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    log_filename = f"logs/reconciliation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)


def calculate_file_hash(file_content: bytes) -> str:
    """Calculate MD5 hash of file content for duplicate detection"""
    return hashlib.md5(file_content).hexdigest()


def detect_file_encoding(file_content: bytes) -> str:
    """Detect file encoding for CSV files"""
    result = chardet.detect(file_content)
    return result['encoding'] if result['encoding'] else 'utf-8'


def get_excel_sheet_names(file_path: str) -> List[str]:
    """Get all sheet names from an Excel file"""
    try:
        excel_file = pd.ExcelFile(file_path)
        return excel_file.sheet_names
    except Exception as e:
        logging.error(f"Error reading Excel file {file_path}: {str(e)}")
        return []


def read_file_data(file_path: str, sheet_name: str = None) -> pd.DataFrame:
    """Read data from Excel or CSV file"""
    try:
        file_ext = os.path.splitext(file_path)[1].lower()
        
        if file_ext in ['.xlsx', '.xls']:
            if sheet_name:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
            else:
                # Read first sheet if no sheet specified
                df = pd.read_excel(file_path)
        elif file_ext == '.csv':
            # Detect encoding for CSV files
            with open(file_path, 'rb') as f:
                encoding = detect_file_encoding(f.read())
            df = pd.read_csv(file_path, encoding=encoding)
        else:
            raise ValueError(f"Unsupported file format: {file_ext}")
        
        # Basic data cleaning
        df = clean_dataframe(df)
        return df
        
    except Exception as e:
        logging.error(f"Error reading file {file_path}: {str(e)}")
        raise


def clean_dataframe(df: pd.DataFrame) -> pd.DataFrame:
    """Clean dataframe by removing empty rows/columns and trimming whitespace"""
    # Remove completely empty rows and columns
    df = df.dropna(how='all').dropna(axis=1, how='all')
    
    # Trim whitespace from string columns
    for col in df.columns:
        if df[col].dtype == 'object':
            df[col] = df[col].astype(str).str.strip()
            # Replace 'nan' strings with actual NaN
            df[col] = df[col].replace('nan', np.nan)
    
    # Reset index
    df = df.reset_index(drop=True)
    
    return df


def load_column_mappings() -> Dict:
    """Load column mappings from config file"""
    config_path = 'config/column_mappings.json'
    try:
        with open(config_path, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        logging.warning(f"Config file {config_path} not found. Using default mappings.")
        return {
            "broker_mappings": {},
            "trader_mappings": {},
            "standard_columns": [
                "Date", "Underlying", "Exchange", "Expiry", "Strike", 
                "Option Type", "Buy Qty", "Buy Value", "Sell Qty", "Sell Value"
            ],
            "common_variations": {}
        }


def save_column_mappings(mappings: Dict):
    """Save column mappings to config file"""
    config_path = 'config/column_mappings.json'
    try:
        with open(config_path, 'w') as f:
            json.dump(mappings, f, indent=2)
        logging.info(f"Column mappings saved to {config_path}")
    except Exception as e:
        logging.error(f"Error saving column mappings: {str(e)}")


def fuzzy_match_columns(df_columns: List[str], standard_columns: List[str], 
                       common_variations: Dict, threshold: int = 80) -> Dict[str, str]:
    """
    Use fuzzy matching to automatically map dataframe columns to standard columns
    Returns dict mapping df_column -> standard_column
    """
    mappings = {}
    
    for std_col in standard_columns:
        # Create list of possible variations for this standard column
        variations = [std_col.lower()]
        if std_col in common_variations:
            variations.extend([v.lower() for v in common_variations[std_col]])
        
        # Find best match among dataframe columns
        best_match = None
        best_score = 0
        
        for df_col in df_columns:
            df_col_lower = df_col.lower()
            
            # Check exact matches first
            if df_col_lower in variations:
                mappings[df_col] = std_col
                break
            
            # Use fuzzy matching
            for variation in variations:
                score = fuzz.ratio(df_col_lower, variation)
                if score > best_score and score >= threshold:
                    best_score = score
                    best_match = df_col
        
        if best_match and std_col not in mappings.values():
            mappings[best_match] = std_col
    
    return mappings


def validate_data_types(df: pd.DataFrame, column_mappings: Dict[str, str]) -> pd.DataFrame:
    """Validate and convert data types for mapped columns"""
    df_copy = df.copy()
    
    for df_col, std_col in column_mappings.items():
        if df_col not in df_copy.columns:
            continue
            
        try:
            if std_col == 'Date':
                df_copy[df_col] = pd.to_datetime(df_copy[df_col], errors='coerce')
            elif std_col in ['Buy Qty', 'Sell Qty']:
                df_copy[df_col] = pd.to_numeric(df_copy[df_col], errors='coerce')
            elif std_col in ['Buy Value', 'Sell Value', 'Strike']:
                df_copy[df_col] = pd.to_numeric(df_copy[df_col], errors='coerce')
            # String columns (Underlying, Exchange, Option Type) remain as-is
                
        except Exception as e:
            logging.warning(f"Error converting column {df_col} to appropriate type: {str(e)}")
    
    return df_copy


def standardize_data(df: pd.DataFrame, column_mappings: Dict[str, str]) -> pd.DataFrame:
    """Transform dataframe to standard schema using column mappings"""
    standard_columns = [
        "Date", "Underlying", "Exchange", "Expiry", "Strike", 
        "Option Type", "Buy Qty", "Buy Value", "Sell Qty", "Sell Value"
    ]
    
    # Create new dataframe with standard columns
    standardized_df = pd.DataFrame(columns=standard_columns)
    
    # Map data from original columns to standard columns
    for df_col, std_col in column_mappings.items():
        if df_col in df.columns and std_col in standard_columns:
            standardized_df[std_col] = df[df_col]
    
    # Fill missing columns with NaN
    for col in standard_columns:
        if col not in standardized_df.columns:
            standardized_df[col] = np.nan
    
    return standardized_df


def save_processed_data(broker_data: pd.DataFrame, trader_data: pd.DataFrame):
    """Save processed data to output CSV files"""
    try:
        if not os.path.exists('output'):
            os.makedirs('output')

        broker_path = 'output/broker_data.csv'
        trader_path = 'output/trader_data.csv'

        broker_data.to_csv(broker_path, index=False)
        trader_data.to_csv(trader_path, index=False)

        logging.info(f"Broker data saved to {broker_path} ({len(broker_data)} rows)")
        logging.info(f"Trader data saved to {trader_path} ({len(trader_data)} rows)")

        return broker_path, trader_path

    except Exception as e:
        logging.error(f"Error saving processed data: {str(e)}")
        raise


class FileProcessor:
    """Class to handle file processing operations"""

    def __init__(self):
        self.uploaded_files = {}
        self.file_hashes = set()
        self.logger = setup_logging()

    def is_supported_file(self, filename: str) -> bool:
        """Check if file format is supported"""
        supported_extensions = ['.xlsx', '.xls', '.csv']
        file_ext = os.path.splitext(filename)[1].lower()
        return file_ext in supported_extensions

    def is_duplicate_file(self, file_content: bytes) -> bool:
        """Check if file is duplicate using hash"""
        file_hash = calculate_file_hash(file_content)
        if file_hash in self.file_hashes:
            return True
        self.file_hashes.add(file_hash)
        return False

    def save_uploaded_file(self, uploaded_file, file_type: str) -> str:
        """Save uploaded file to input_files directory"""
        if not os.path.exists('input_files'):
            os.makedirs('input_files')

        # Create subdirectories for broker and trader files
        subdir = os.path.join('input_files', file_type)
        if not os.path.exists(subdir):
            os.makedirs(subdir)

        file_path = os.path.join(subdir, uploaded_file.name)

        with open(file_path, 'wb') as f:
            f.write(uploaded_file.getvalue())

        self.logger.info(f"Saved {file_type} file: {file_path}")
        return file_path

    def process_uploaded_files(self, uploaded_files: List, file_type: str) -> Dict:
        """Process multiple uploaded files and return file information"""
        processed_files = {}

        for uploaded_file in uploaded_files:
            if not self.is_supported_file(uploaded_file.name):
                self.logger.warning(f"Unsupported file format: {uploaded_file.name}")
                continue

            file_content = uploaded_file.getvalue()

            if self.is_duplicate_file(file_content):
                self.logger.warning(f"Duplicate file detected: {uploaded_file.name}")
                continue

            try:
                file_path = self.save_uploaded_file(uploaded_file, file_type)

                file_info = {
                    'path': file_path,
                    'name': uploaded_file.name,
                    'size': len(file_content),
                    'type': file_type
                }

                # Get sheet names for Excel files
                if uploaded_file.name.lower().endswith(('.xlsx', '.xls')):
                    sheet_names = get_excel_sheet_names(file_path)
                    file_info['sheets'] = sheet_names
                    file_info['selected_sheet'] = sheet_names[0] if sheet_names else None
                else:
                    file_info['sheets'] = None
                    file_info['selected_sheet'] = None

                processed_files[uploaded_file.name] = file_info

            except Exception as e:
                self.logger.error(f"Error processing file {uploaded_file.name}: {str(e)}")
                continue

        return processed_files


class ColumnMapper:
    """Class to handle intelligent column mapping"""

    def __init__(self):
        self.config = load_column_mappings()
        self.logger = setup_logging()

    def get_auto_mappings(self, df_columns: List[str], file_type: str) -> Tuple[Dict[str, str], List[str]]:
        """
        Get automatic column mappings using fuzzy matching
        Returns: (mappings_dict, unmapped_columns)
        """
        standard_columns = self.config['standard_columns']
        common_variations = self.config.get('common_variations', {})

        # Check for saved mappings first
        saved_mappings_key = f"{file_type}_mappings"
        saved_mappings = self.config.get(saved_mappings_key, {})

        auto_mappings = {}
        unmapped_columns = []

        # Apply saved mappings first
        for df_col in df_columns:
            if df_col in saved_mappings:
                auto_mappings[df_col] = saved_mappings[df_col]

        # Use fuzzy matching for unmapped columns
        remaining_columns = [col for col in df_columns if col not in auto_mappings]
        remaining_standards = [col for col in standard_columns if col not in auto_mappings.values()]

        fuzzy_mappings = fuzzy_match_columns(
            remaining_columns,
            remaining_standards,
            common_variations,
            threshold=75
        )

        auto_mappings.update(fuzzy_mappings)

        # Identify unmapped columns
        for col in df_columns:
            if col not in auto_mappings:
                unmapped_columns.append(col)

        self.logger.info(f"Auto-mapped {len(auto_mappings)} columns, {len(unmapped_columns)} require manual mapping")

        return auto_mappings, unmapped_columns

    def save_user_mappings(self, mappings: Dict[str, str], file_type: str):
        """Save user-defined column mappings to config"""
        mappings_key = f"{file_type}_mappings"

        # Update existing mappings
        if mappings_key not in self.config:
            self.config[mappings_key] = {}

        self.config[mappings_key].update(mappings)

        # Save to file
        save_column_mappings(self.config)
        self.logger.info(f"Saved {len(mappings)} user mappings for {file_type}")

    def get_mapping_confidence(self, df_columns: List[str], mappings: Dict[str, str]) -> float:
        """Calculate confidence score for column mappings (0-1)"""
        if not df_columns:
            return 0.0

        mapped_count = len(mappings)
        total_count = len(self.config['standard_columns'])

        # Base confidence on percentage of standard columns mapped
        confidence = mapped_count / total_count

        return min(confidence, 1.0)

    def validate_mappings(self, mappings: Dict[str, str]) -> Tuple[bool, List[str]]:
        """
        Validate column mappings
        Returns: (is_valid, error_messages)
        """
        errors = []
        standard_columns = self.config['standard_columns']

        # Check for duplicate standard column assignments
        mapped_standards = list(mappings.values())
        duplicates = [col for col in mapped_standards if mapped_standards.count(col) > 1]
        if duplicates:
            errors.append(f"Duplicate mappings for standard columns: {', '.join(set(duplicates))}")

        # Check for invalid standard columns
        invalid_standards = [col for col in mapped_standards if col not in standard_columns]
        if invalid_standards:
            errors.append(f"Invalid standard columns: {', '.join(invalid_standards)}")

        # Check for required columns (Date is typically required)
        required_columns = ['Date']
        missing_required = [col for col in required_columns if col not in mapped_standards]
        if missing_required:
            errors.append(f"Missing required columns: {', '.join(missing_required)}")

        is_valid = len(errors) == 0
        return is_valid, errors


class DataStandardizer:
    """Class to handle data standardization and transformation"""

    def __init__(self):
        self.logger = setup_logging()
        self.standard_columns = [
            "Date", "Underlying", "Exchange", "Expiry", "Strike",
            "Option Type", "Buy Qty", "Buy Value", "Sell Qty", "Sell Value"
        ]

    def standardize_dataframe(self, df: pd.DataFrame, column_mappings: Dict[str, str]) -> pd.DataFrame:
        """Transform dataframe to standard schema using column mappings"""
        try:
            # Validate and convert data types first
            df_validated = validate_data_types(df, column_mappings)

            # Create standardized dataframe
            standardized_df = pd.DataFrame()

            # Map columns to standard schema
            for df_col, std_col in column_mappings.items():
                if df_col in df_validated.columns and std_col in self.standard_columns:
                    standardized_df[std_col] = df_validated[df_col]

            # Add missing standard columns with NaN values
            for col in self.standard_columns:
                if col not in standardized_df.columns:
                    standardized_df[col] = np.nan

            # Apply specific data cleaning and formatting
            standardized_df = self._apply_data_formatting(standardized_df)

            # Remove completely empty rows
            standardized_df = standardized_df.dropna(how='all')

            self.logger.info(f"Standardized dataframe: {len(standardized_df)} rows, {len(standardized_df.columns)} columns")

            return standardized_df

        except Exception as e:
            self.logger.error(f"Error standardizing dataframe: {str(e)}")
            raise

    def _apply_data_formatting(self, df: pd.DataFrame) -> pd.DataFrame:
        """Apply specific formatting rules to standardized data"""
        df_copy = df.copy()

        try:
            # Format Date column
            if 'Date' in df_copy.columns:
                df_copy['Date'] = pd.to_datetime(df_copy['Date'], errors='coerce')
                df_copy['Date'] = df_copy['Date'].dt.strftime('%Y-%m-%d')

            # Format Option Type column (standardize to CALL/PUT)
            if 'Option Type' in df_copy.columns:
                df_copy['Option Type'] = df_copy['Option Type'].astype(str).str.upper()
                df_copy['Option Type'] = df_copy['Option Type'].replace({
                    'C': 'CALL', 'CALL': 'CALL', 'CE': 'CALL',
                    'P': 'PUT', 'PUT': 'PUT', 'PE': 'PUT'
                })

            # Format numeric columns
            numeric_columns = ['Strike', 'Buy Qty', 'Buy Value', 'Sell Qty', 'Sell Value']
            for col in numeric_columns:
                if col in df_copy.columns:
                    df_copy[col] = pd.to_numeric(df_copy[col], errors='coerce')
                    # Round to 2 decimal places for values, whole numbers for quantities
                    if 'Value' in col or col == 'Strike':
                        df_copy[col] = df_copy[col].round(2)
                    else:  # Quantity columns
                        df_copy[col] = df_copy[col].round(0)

            # Format string columns (trim and standardize case)
            string_columns = ['Underlying', 'Exchange']
            for col in string_columns:
                if col in df_copy.columns:
                    df_copy[col] = df_copy[col].astype(str).str.strip().str.upper()
                    df_copy[col] = df_copy[col].replace('NAN', np.nan)

            # Format Expiry column (try to standardize date format)
            if 'Expiry' in df_copy.columns:
                try:
                    df_copy['Expiry'] = pd.to_datetime(df_copy['Expiry'], errors='coerce')
                    df_copy['Expiry'] = df_copy['Expiry'].dt.strftime('%Y-%m-%d')
                except:
                    # If date conversion fails, keep as string
                    df_copy['Expiry'] = df_copy['Expiry'].astype(str).str.strip()

        except Exception as e:
            self.logger.warning(f"Error in data formatting: {str(e)}")

        return df_copy

    def combine_datasets(self, datasets: List[pd.DataFrame]) -> pd.DataFrame:
        """Combine multiple standardized datasets into one"""
        if not datasets:
            return pd.DataFrame(columns=self.standard_columns)

        try:
            combined_df = pd.concat(datasets, ignore_index=True)

            # Remove duplicate rows
            combined_df = combined_df.drop_duplicates()

            # Sort by Date if available
            if 'Date' in combined_df.columns:
                combined_df['Date'] = pd.to_datetime(combined_df['Date'], errors='coerce')
                combined_df = combined_df.sort_values('Date', na_last=True)
                combined_df['Date'] = combined_df['Date'].dt.strftime('%Y-%m-%d')

            combined_df = combined_df.reset_index(drop=True)

            self.logger.info(f"Combined {len(datasets)} datasets into {len(combined_df)} rows")

            return combined_df

        except Exception as e:
            self.logger.error(f"Error combining datasets: {str(e)}")
            raise

    def validate_standardized_data(self, df: pd.DataFrame) -> Tuple[bool, List[str]]:
        """Validate standardized data quality"""
        issues = []

        # Check if dataframe is empty
        if df.empty:
            issues.append("Dataset is empty")
            return False, issues

        # Check for required columns
        missing_cols = [col for col in self.standard_columns if col not in df.columns]
        if missing_cols:
            issues.append(f"Missing standard columns: {', '.join(missing_cols)}")

        # Check data quality
        if 'Date' in df.columns:
            invalid_dates = df['Date'].isna().sum()
            if invalid_dates > len(df) * 0.5:  # More than 50% invalid dates
                issues.append(f"High number of invalid dates: {invalid_dates}/{len(df)}")

        # Check for completely empty rows
        empty_rows = df.isna().all(axis=1).sum()
        if empty_rows > 0:
            issues.append(f"Found {empty_rows} completely empty rows")

        is_valid = len(issues) == 0
        return is_valid, issues


class ErrorHandler:
    """Centralized error handling and user-friendly error messages"""

    def __init__(self):
        self.logger = setup_logging()

    @staticmethod
    def get_user_friendly_error(error: Exception) -> str:
        """Convert technical errors to user-friendly messages"""
        error_str = str(error).lower()

        if 'permission denied' in error_str:
            return "❌ Permission denied. Please check if the file is open in another application and close it."

        elif 'file not found' in error_str or 'no such file' in error_str:
            return "❌ File not found. Please ensure the file exists and try uploading again."

        elif 'unsupported format' in error_str or 'excel file format' in error_str:
            return "❌ Unsupported file format. Please use .xlsx, .xls, or .csv files only."

        elif 'encoding' in error_str or 'codec' in error_str:
            return "❌ File encoding issue. The file may be corrupted or use an unsupported character encoding."

        elif 'memory' in error_str or 'out of memory' in error_str:
            return "❌ File too large. Please try processing smaller files or contact support."

        elif 'empty' in error_str or 'no data' in error_str:
            return "❌ The file appears to be empty or contains no readable data."

        elif 'sheet' in error_str and 'not found' in error_str:
            return "❌ The selected sheet was not found in the Excel file. Please select a valid sheet."

        elif 'column' in error_str and ('not found' in error_str or 'missing' in error_str):
            return "❌ Required columns are missing. Please check your column mappings."

        elif 'date' in error_str and 'parse' in error_str:
            return "❌ Date format issue. Please ensure dates are in a recognizable format (YYYY-MM-DD, MM/DD/YYYY, etc.)."

        elif 'network' in error_str or 'connection' in error_str:
            return "❌ Network connection issue. Please check your internet connection and try again."

        else:
            return f"❌ An unexpected error occurred: {str(error)}"

    def log_error(self, error: Exception, context: str = ""):
        """Log error with context information"""
        error_msg = f"Error in {context}: {str(error)}" if context else str(error)
        self.logger.error(error_msg, exc_info=True)

    def handle_file_processing_error(self, error: Exception, filename: str) -> str:
        """Handle file processing specific errors"""
        self.log_error(error, f"processing file {filename}")

        error_str = str(error).lower()

        if 'xlrd' in error_str or 'openpyxl' in error_str:
            return f"❌ Error reading Excel file '{filename}'. The file may be corrupted or password-protected."

        elif 'pandas' in error_str and 'parse' in error_str:
            return f"❌ Error parsing data in '{filename}'. Please check the file format and content."

        elif 'sheet' in error_str:
            return f"❌ Error accessing sheet in '{filename}'. Please verify the sheet exists and is not empty."

        else:
            return self.get_user_friendly_error(error)

    def handle_column_mapping_error(self, error: Exception, filename: str) -> str:
        """Handle column mapping specific errors"""
        self.log_error(error, f"column mapping for {filename}")

        error_str = str(error).lower()

        if 'fuzzy' in error_str or 'matching' in error_str:
            return f"❌ Error in automatic column matching for '{filename}'. Please use manual mapping."

        elif 'duplicate' in error_str:
            return f"❌ Duplicate column mappings detected in '{filename}'. Each standard column can only be mapped once."

        elif 'required' in error_str:
            return f"❌ Required columns missing in '{filename}'. Please ensure Date column is mapped."

        else:
            return self.get_user_friendly_error(error)

    def handle_data_standardization_error(self, error: Exception, filename: str) -> str:
        """Handle data standardization specific errors"""
        self.log_error(error, f"data standardization for {filename}")

        error_str = str(error).lower()

        if 'dtype' in error_str or 'conversion' in error_str:
            return f"❌ Data type conversion error in '{filename}'. Please check numeric and date columns."

        elif 'validation' in error_str:
            return f"❌ Data validation failed for '{filename}'. Please review the data quality."

        else:
            return self.get_user_friendly_error(error)


class PerformanceMonitor:
    """Monitor system performance and processing times"""

    def __init__(self):
        self.logger = setup_logging()
        self.start_times = {}

    def start_timer(self, operation: str):
        """Start timing an operation"""
        self.start_times[operation] = time.time()
        self.logger.info(f"Started {operation}")

    def end_timer(self, operation: str) -> float:
        """End timing an operation and return duration"""
        if operation not in self.start_times:
            return 0.0

        duration = time.time() - self.start_times[operation]
        self.logger.info(f"Completed {operation} in {duration:.2f} seconds")
        del self.start_times[operation]
        return duration

    def check_file_size_limits(self, file_size: int, filename: str) -> bool:
        """Check if file size is within acceptable limits"""
        max_size_mb = 50  # 50MB limit
        max_size_bytes = max_size_mb * 1024 * 1024

        if file_size > max_size_bytes:
            self.logger.warning(f"File {filename} exceeds size limit: {file_size / (1024*1024):.1f}MB")
            return False

        return True

    def estimate_processing_time(self, total_files: int, total_size_mb: float) -> float:
        """Estimate processing time based on file count and size"""
        # Rough estimates: 1 second per file + 0.5 seconds per MB
        base_time = total_files * 1.0
        size_time = total_size_mb * 0.5
        estimated_time = base_time + size_time

        self.logger.info(f"Estimated processing time: {estimated_time:.1f} seconds for {total_files} files ({total_size_mb:.1f}MB)")
        return estimated_time
