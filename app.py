import streamlit as st
import pandas as pd
import os
from datetime import datetime
from typing import Dict, List, Tuple
import time

from utils import (
    FileProcessor, ColumnMapper, DataStandardizer, ErrorHandler, PerformanceMonitor,
    setup_logging, read_file_data, save_processed_data
)

# Page configuration
st.set_page_config(
    page_title="Financial Data Reconciliation System",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if 'file_processor' not in st.session_state:
    st.session_state.file_processor = FileProcessor()
if 'column_mapper' not in st.session_state:
    st.session_state.column_mapper = ColumnMapper()
if 'data_standardizer' not in st.session_state:
    st.session_state.data_standardizer = DataStandardizer()
if 'error_handler' not in st.session_state:
    st.session_state.error_handler = ErrorHandler()
if 'performance_monitor' not in st.session_state:
    st.session_state.performance_monitor = PerformanceMonitor()
if 'processed_files' not in st.session_state:
    st.session_state.processed_files = {'broker': {}, 'trader': {}}
if 'processing_complete' not in st.session_state:
    st.session_state.processing_complete = False

def main():
    st.title("📊 Financial Data Reconciliation System")
    st.markdown("---")
    
    # Sidebar for navigation
    st.sidebar.title("Navigation")
    step = st.sidebar.radio(
        "Select Step:",
        ["1. File Upload", "2. Column Mapping", "3. Data Processing", "4. Results & Download"]
    )
    
    if step == "1. File Upload":
        file_upload_step()
    elif step == "2. Column Mapping":
        column_mapping_step()
    elif step == "3. Data Processing":
        data_processing_step()
    elif step == "4. Results & Download":
        results_download_step()

def file_upload_step():
    st.header("Step 1: File Upload")
    st.markdown("Upload your broker and trader Excel/CSV files for processing.")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("📈 Broker Files")
        broker_files = st.file_uploader(
            "Upload Broker Files",
            type=['xlsx', 'xls', 'csv'],
            accept_multiple_files=True,
            key="broker_files"
        )
        
        if broker_files:
            process_uploaded_files(broker_files, 'broker')
    
    with col2:
        st.subheader("📉 Trader Files")
        trader_files = st.file_uploader(
            "Upload Trader Files",
            type=['xlsx', 'xls', 'csv'],
            accept_multiple_files=True,
            key="trader_files"
        )
        
        if trader_files:
            process_uploaded_files(trader_files, 'trader')
    
    # Display uploaded files summary
    display_uploaded_files_summary()

def process_uploaded_files(uploaded_files: List, file_type: str):
    """Process uploaded files and update session state"""
    if uploaded_files:
        # Check file size limits
        total_size_mb = sum(len(f.getvalue()) for f in uploaded_files) / (1024 * 1024)

        if total_size_mb > 100:  # 100MB total limit
            st.error("❌ Total file size exceeds 100MB limit. Please upload smaller files.")
            return

        # Estimate processing time
        estimated_time = st.session_state.performance_monitor.estimate_processing_time(
            len(uploaded_files), total_size_mb
        )

        if estimated_time > 30:
            st.warning(f"⚠️ Estimated processing time: {estimated_time:.1f} seconds. Large files may take longer.")

        with st.spinner(f"Processing {file_type} files..."):
            try:
                st.session_state.performance_monitor.start_timer(f"process_{file_type}_files")

                processed = st.session_state.file_processor.process_uploaded_files(
                    uploaded_files, file_type
                )
                st.session_state.processed_files[file_type].update(processed)

                duration = st.session_state.performance_monitor.end_timer(f"process_{file_type}_files")

                if processed:
                    st.success(f"✅ Successfully processed {len(processed)} {file_type} file(s) in {duration:.1f}s")

                    # Handle Excel sheet selection
                    handle_sheet_selection(file_type)
                else:
                    st.warning(f"⚠️ No valid {file_type} files were processed. Please check file formats and content.")

            except Exception as e:
                error_msg = st.session_state.error_handler.handle_file_processing_error(e, f"{file_type} files")
                st.error(error_msg)

def handle_sheet_selection(file_type: str):
    """Handle Excel sheet selection for uploaded files"""
    files_with_sheets = {
        name: info for name, info in st.session_state.processed_files[file_type].items()
        if info.get('sheets')
    }
    
    if files_with_sheets:
        st.subheader(f"Select Sheets for {file_type.title()} Files")
        
        for file_name, file_info in files_with_sheets.items():
            if len(file_info['sheets']) > 1:
                selected_sheet = st.selectbox(
                    f"Select sheet for {file_name}:",
                    file_info['sheets'],
                    index=0,
                    key=f"sheet_{file_type}_{file_name}"
                )
                st.session_state.processed_files[file_type][file_name]['selected_sheet'] = selected_sheet

def display_uploaded_files_summary():
    """Display summary of uploaded files"""
    st.markdown("---")
    st.subheader("📋 Uploaded Files Summary")
    
    total_broker = len(st.session_state.processed_files['broker'])
    total_trader = len(st.session_state.processed_files['trader'])
    
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Broker Files", total_broker)
    with col2:
        st.metric("Trader Files", total_trader)
    with col3:
        st.metric("Total Files", total_broker + total_trader)
    
    if total_broker > 0 or total_trader > 0:
        # Display file details
        for file_type in ['broker', 'trader']:
            if st.session_state.processed_files[file_type]:
                st.write(f"**{file_type.title()} Files:**")
                for name, info in st.session_state.processed_files[file_type].items():
                    sheet_info = f" (Sheet: {info['selected_sheet']})" if info.get('selected_sheet') else ""
                    st.write(f"- {name}{sheet_info} ({info['size']} bytes)")

def column_mapping_step():
    st.header("Step 2: Column Mapping")
    
    if not any(st.session_state.processed_files.values()):
        st.warning("⚠️ Please upload files first in Step 1.")
        return
    
    st.markdown("Map your file columns to the standard schema.")
    
    # Display standard schema
    with st.expander("📋 Standard Schema", expanded=True):
        standard_cols = st.session_state.column_mapper.config['standard_columns']
        st.write("**Required Columns:**")
        for i, col in enumerate(standard_cols, 1):
            st.write(f"{i}. {col}")
    
    # Process column mapping for each file type
    for file_type in ['broker', 'trader']:
        if st.session_state.processed_files[file_type]:
            st.subheader(f"🔗 {file_type.title()} Column Mapping")
            process_column_mapping(file_type)

def process_column_mapping(file_type: str):
    """Process column mapping for a specific file type"""
    files = st.session_state.processed_files[file_type]
    
    for file_name, file_info in files.items():
        with st.expander(f"Map columns for {file_name}", expanded=True):
            try:
                # Read file data
                df = read_file_data(file_info['path'], file_info.get('selected_sheet'))
                
                if df.empty:
                    st.warning(f"No data found in {file_name}")
                    continue
                
                # Get automatic mappings
                auto_mappings, unmapped_cols = st.session_state.column_mapper.get_auto_mappings(
                    list(df.columns), file_type
                )
                
                # Display confidence score
                confidence = st.session_state.column_mapper.get_mapping_confidence(
                    list(df.columns), auto_mappings
                )
                
                col1, col2 = st.columns([3, 1])
                with col1:
                    st.write(f"**Auto-mapping confidence:** {confidence:.1%}")
                with col2:
                    if confidence >= 0.8:
                        st.success("High")
                    elif confidence >= 0.6:
                        st.warning("Medium")
                    else:
                        st.error("Low")
                
                # Manual mapping interface
                final_mappings = create_mapping_interface(df.columns, auto_mappings, file_name)
                
                # Store mappings in session state
                if f'mappings_{file_type}' not in st.session_state:
                    st.session_state[f'mappings_{file_type}'] = {}
                st.session_state[f'mappings_{file_type}'][file_name] = final_mappings
                
                # Preview mapped data
                if st.button(f"Preview Mapped Data for {file_name}", key=f"preview_{file_type}_{file_name}"):
                    preview_mapped_data(df, final_mappings)
                
            except Exception as e:
                st.error(f"Error processing {file_name}: {str(e)}")

def create_mapping_interface(df_columns: List[str], auto_mappings: Dict[str, str], file_name: str) -> Dict[str, str]:
    """Create interactive column mapping interface"""
    standard_columns = st.session_state.column_mapper.config['standard_columns']
    
    st.write("**Column Mappings:**")
    
    final_mappings = {}
    
    # Create mapping dropdowns
    for df_col in df_columns:
        col1, col2, col3 = st.columns([2, 1, 2])
        
        with col1:
            st.write(f"📄 {df_col}")
        
        with col2:
            st.write("→")
        
        with col3:
            # Get current mapping (auto or none)
            current_mapping = auto_mappings.get(df_col, "")
            
            # Create options list
            options = [""] + standard_columns
            default_index = options.index(current_mapping) if current_mapping in options else 0
            
            selected_mapping = st.selectbox(
                "Standard Column",
                options,
                index=default_index,
                key=f"mapping_{file_name}_{df_col}",
                label_visibility="collapsed"
            )
            
            if selected_mapping:
                final_mappings[df_col] = selected_mapping
    
    return final_mappings

def preview_mapped_data(df: pd.DataFrame, mappings: Dict[str, str]):
    """Preview data with applied mappings"""
    if not mappings:
        st.warning("No column mappings defined.")
        return
    
    try:
        standardized_df = st.session_state.data_standardizer.standardize_dataframe(df, mappings)
        
        st.write("**Preview of Mapped Data (first 10 rows):**")
        st.dataframe(standardized_df.head(10), use_container_width=True)
        
        # Show mapping summary
        st.write("**Applied Mappings:**")
        for df_col, std_col in mappings.items():
            st.write(f"- {df_col} → {std_col}")
            
    except Exception as e:
        st.error(f"Error previewing mapped data: {str(e)}")

def data_processing_step():
    st.header("Step 3: Data Processing")
    
    # Check if mappings are available
    broker_mappings = st.session_state.get('mappings_broker', {})
    trader_mappings = st.session_state.get('mappings_trader', {})
    
    if not broker_mappings and not trader_mappings:
        st.warning("⚠️ Please complete column mapping in Step 2 first.")
        return
    
    st.markdown("Process and standardize your data according to the mapped columns.")
    
    if st.button("🚀 Start Data Processing", type="primary"):
        process_all_data()

def process_all_data():
    """Process all uploaded files and generate standardized output"""
    try:
        with st.spinner("Processing data..."):
            progress_bar = st.progress(0)
            status_text = st.empty()
            
            broker_datasets = []
            trader_datasets = []
            
            total_files = (len(st.session_state.processed_files['broker']) + 
                          len(st.session_state.processed_files['trader']))
            processed_count = 0
            
            # Process broker files
            for file_name, file_info in st.session_state.processed_files['broker'].items():
                status_text.text(f"Processing broker file: {file_name}")
                
                mappings = st.session_state.get('mappings_broker', {}).get(file_name, {})
                if mappings:
                    df = read_file_data(file_info['path'], file_info.get('selected_sheet'))
                    standardized_df = st.session_state.data_standardizer.standardize_dataframe(df, mappings)
                    broker_datasets.append(standardized_df)
                
                processed_count += 1
                progress_bar.progress(processed_count / total_files)
            
            # Process trader files
            for file_name, file_info in st.session_state.processed_files['trader'].items():
                status_text.text(f"Processing trader file: {file_name}")
                
                mappings = st.session_state.get('mappings_trader', {}).get(file_name, {})
                if mappings:
                    df = read_file_data(file_info['path'], file_info.get('selected_sheet'))
                    standardized_df = st.session_state.data_standardizer.standardize_dataframe(df, mappings)
                    trader_datasets.append(standardized_df)
                
                processed_count += 1
                progress_bar.progress(processed_count / total_files)
            
            # Combine datasets
            status_text.text("Combining datasets...")
            broker_combined = st.session_state.data_standardizer.combine_datasets(broker_datasets)
            trader_combined = st.session_state.data_standardizer.combine_datasets(trader_datasets)
            
            # Save processed data
            status_text.text("Saving processed data...")
            broker_path, trader_path = save_processed_data(broker_combined, trader_combined)
            
            # Store results in session state
            st.session_state.broker_data = broker_combined
            st.session_state.trader_data = trader_combined
            st.session_state.processing_complete = True
            
            progress_bar.progress(1.0)
            status_text.text("Processing complete!")
            
            st.success(f"✅ Processing completed successfully!")
            st.info(f"📊 Broker data: {len(broker_combined)} rows")
            st.info(f"📊 Trader data: {len(trader_combined)} rows")
            
    except Exception as e:
        st.error(f"❌ Error during processing: {str(e)}")

def results_download_step():
    st.header("Step 4: Results & Download")
    
    if not st.session_state.processing_complete:
        st.warning("⚠️ Please complete data processing in Step 3 first.")
        return
    
    st.markdown("Review your processed data and download the results.")
    
    # Display data previews
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("📈 Broker Data")
        if 'broker_data' in st.session_state:
            broker_data = st.session_state.broker_data
            st.write(f"**Total rows:** {len(broker_data)}")
            st.dataframe(broker_data.head(10), use_container_width=True)
            
            # Download button
            csv_broker = broker_data.to_csv(index=False)
            st.download_button(
                label="📥 Download Broker Data CSV",
                data=csv_broker,
                file_name=f"broker_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv",
                type="primary"
            )
    
    with col2:
        st.subheader("📉 Trader Data")
        if 'trader_data' in st.session_state:
            trader_data = st.session_state.trader_data
            st.write(f"**Total rows:** {len(trader_data)}")
            st.dataframe(trader_data.head(10), use_container_width=True)
            
            # Download button
            csv_trader = trader_data.to_csv(index=False)
            st.download_button(
                label="📥 Download Trader Data CSV",
                data=csv_trader,
                file_name=f"trader_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv",
                type="primary"
            )
    
    # Processing summary
    st.markdown("---")
    st.subheader("📋 Processing Summary")
    
    total_broker_files = len(st.session_state.processed_files['broker'])
    total_trader_files = len(st.session_state.processed_files['trader'])
    total_broker_rows = len(st.session_state.broker_data) if 'broker_data' in st.session_state else 0
    total_trader_rows = len(st.session_state.trader_data) if 'trader_data' in st.session_state else 0
    
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("Broker Files", total_broker_files)
    with col2:
        st.metric("Trader Files", total_trader_files)
    with col3:
        st.metric("Broker Records", total_broker_rows)
    with col4:
        st.metric("Trader Records", total_trader_rows)

if __name__ == "__main__":
    # Setup logging
    logger = setup_logging()
    logger.info("Starting Financial Data Reconciliation System")
    
    main()
